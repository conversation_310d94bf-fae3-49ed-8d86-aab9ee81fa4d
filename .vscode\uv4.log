*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\keil5\ARM\ARMCompiler_506_Windows_x86_b960\Bin'
Build target '24H_practice'
Note: source file '..\User\Driver\mpu6050_driver.c' - object file renamed from '24H_practice\mpu6050_driver.o' to '24H_practice\mpu6050_driver_1.o'.compiling inv_mpu.c...
compiling mpu6050_app.c...
compiling motor_driver.c...
compiling main.c...
compiling encoder_app.c...
compiling encoder_driver.c...
compiling Scheduler_Task.c...
compiling Scheduler.c...
compiling pid_app.c...
compiling motor_app.c...
compiling mpu6050_driver.c...
compiling uart_app.c...
compiling inv_mpu_dmp_motion_driver.c...
linking...
Program Size: Code=29174 RO-data=3842 RW-data=220 ZI-data=2476  
FromELF: creating hex file...
"24H_practice\24H_practice.axf" - 0 Error(s), 0 Warning(s).
Build Time Elapsed:  00:00:14
