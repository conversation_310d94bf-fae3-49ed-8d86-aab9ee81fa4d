*** Using Compiler 'V5.06 update 7 (build 960)', folder: 'E:\keil5\ARM\ARMCompiler_506_Windows_x86_b960\Bin'
Build target '24H_practice'
compiling pid_app.c...
..\User\App\pid_app.c(125): warning:  #177-D: variable "output_right"  was declared but never referenced
      int output_left = 0, output_right = 0;
..\User\App\pid_app.c: 1 warning, 0 errors
linking...
Program Size: Code=16618 RO-data=458 RW-data=136 ZI-data=2464  
FromELF: creating hex file...
"24H_practice\24H_practice.axf" - 0 Error(s), 1 Warning(s).
Build Time Elapsed:  00:00:04
